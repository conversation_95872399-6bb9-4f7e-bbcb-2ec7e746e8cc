import turtle
import math
import time

# Set up the screen
screen = turtle.Screen()
screen.bgcolor("black")
screen.title("🎬 CLASSIC DVD BOUNCING LOGO 🎬")
screen.setup(width=1000, height=700)
screen.tracer(0)  # Turn off animation for manual control

# DVD Logo colors (classic rainbow cycle)
dvd_colors = [
    "red", "orange", "yellow", "green", "cyan", "blue", "magenta", "purple",
    "lime", "gold", "pink", "lightblue", "lightgreen", "violet"
]

class DVDLogo:
    def __init__(self):
        self.turtle = turtle.Turtle()
        self.turtle.speed(0)
        self.turtle.penup()
        self.turtle.shape("square")
        self.turtle.shapesize(4, 2)  # DVD logo proportions

        # Starting position
        self.x = 0
        self.y = 0

        # Velocity - irregular speeds make corners harder to hit
        self.vx = 4.1  # Slightly non-integer speed
        self.vy = 2.7  # Different speed for y

        # Screen boundaries (accounting for logo size)
        self.max_x = 450
        self.min_x = -450
        self.max_y = 300
        self.min_y = -300

        # Color management
        self.color_index = 0
        self.turtle.color(dvd_colors[self.color_index])

        # Stats
        self.corner_hits = 0
        self.total_bounces = 0

        # Position the logo
        self.turtle.goto(self.x, self.y)

    def update(self):
        """Update logo position and handle bouncing"""
        # Update position
        self.x += self.vx
        self.y += self.vy

        # Check for wall collisions and bounce
        bounced = False

        # Horizontal walls
        if self.x >= self.max_x or self.x <= self.min_x:
            self.vx = -self.vx
            bounced = True

        # Vertical walls
        if self.y >= self.max_y or self.y <= self.min_y:
            self.vy = -self.vy
            bounced = True

        # If bounced, change color and check for corner hit
        if bounced:
            self.total_bounces += 1
            self.change_color()
            self.check_corner_hit()

        # Update turtle position
        self.turtle.goto(self.x, self.y)

    def change_color(self):
        """Change to next color in the cycle"""
        self.color_index = (self.color_index + 1) % len(dvd_colors)
        self.turtle.color(dvd_colors[self.color_index])

    def check_corner_hit(self):
        """Check if logo hit a corner (rare event!)"""
        corner_threshold = 30

        corners = [
            (self.max_x, self.max_y),    # Top right
            (self.max_x, self.min_y),    # Bottom right
            (self.min_x, self.max_y),    # Top left
            (self.min_x, self.min_y)     # Bottom left
        ]

        for corner_x, corner_y in corners:
            distance = math.sqrt((self.x - corner_x)**2 + (self.y - corner_y)**2)
            if distance <= corner_threshold:
                self.corner_hits += 1
                print(f"🎉 CORNER HIT #{self.corner_hits}! 🎉")
                self.celebrate()
                break

    def celebrate(self):
        """Flash effect for corner hit"""
        original_color = dvd_colors[self.color_index]
        for _ in range(4):
            self.turtle.color("white")
            screen.update()
            time.sleep(0.1)
            self.turtle.color("yellow")
            screen.update()
            time.sleep(0.1)
        self.turtle.color(original_color)

def draw_ui(dvd):
    """Draw the UI elements"""
    # Clear and redraw text
    writer = turtle.Turtle()
    writer.speed(0)
    writer.penup()
    writer.hideturtle()
    writer.color("white")

    # Title
    writer.goto(0, 320)
    writer.write("🎬 CLASSIC DVD BOUNCING LOGO 🎬",
                align="center", font=("Arial", 16, "bold"))

    # Stats
    writer.goto(-480, 280)
    writer.write(f"Bounces: {dvd.total_bounces}",
                align="left", font=("Arial", 12, "normal"))

    writer.goto(-480, 260)
    writer.write(f"Corner Hits: {dvd.corner_hits} 🎯",
                align="left", font=("Arial", 12, "normal"))

    if dvd.total_bounces > 0:
        rate = (dvd.corner_hits / dvd.total_bounces) * 100
        writer.goto(-480, 240)
        writer.write(f"Hit Rate: {rate:.2f}%",
                    align="left", font=("Arial", 12, "normal"))

    # Instructions
    writer.goto(0, -320)
    writer.write("Watch for corner hits! Close window to exit.",
                align="center", font=("Arial", 10, "italic"))

    del writer

def main():
    print("🎬 Starting Classic DVD Bouncing Logo Animation!")
    print("🎯 Watch for corner hits - they're very rare!")
    print("🌈 Logo changes color with each bounce")
    print("❌ Close the window to stop")

    # Create DVD logo
    dvd = DVDLogo()

    frame_count = 0

    try:
        while True:
            # Update logo
            dvd.update()

            # Redraw UI every 30 frames to avoid flashing
            if frame_count % 30 == 0:
                screen.clear()
                screen.bgcolor("black")
                draw_ui(dvd)
                # Redraw the logo after clearing
                dvd.turtle.goto(dvd.x, dvd.y)
                dvd.turtle.color(dvd_colors[dvd.color_index])

            # Update screen
            screen.update()
            frame_count += 1

            # Control speed
            time.sleep(0.02)  # ~50 FPS

    except turtle.Terminator:
        print(f"\n🎬 Animation ended!")
        print(f"📊 Final Stats:")
        print(f"   Total Bounces: {dvd.total_bounces}")
        print(f"   Corner Hits: {dvd.corner_hits}")
        if dvd.total_bounces > 0:
            print(f"   Corner Hit Rate: {(dvd.corner_hits/dvd.total_bounces)*100:.2f}%")
        print("Thanks for watching! 🎉")
    except KeyboardInterrupt:
        print("Animation interrupted!")

if __name__ == "__main__":
    main()
