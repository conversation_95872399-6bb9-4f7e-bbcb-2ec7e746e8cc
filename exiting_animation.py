import turtle
import math
import random
import time

# Set up the screen
screen = turtle.Screen()
screen.bgcolor("black")
screen.title("EXTREME 3D ANIMATION")
screen.setup(width=1200, height=800)
screen.tracer(0)  # Turn off animation for manual control

# Create multiple turtles for different effects
turtles = []
for i in range(8):
    t = turtle.Turtle()
    t.speed(0)
    t.pensize(2)
    t.shape("circle")
    t.shapesize(0.5)
    turtles.append(t)

# Color palettes for extreme effects
colors = ["red", "orange", "yellow", "green", "cyan", "blue", "magenta", "white",
          "lime", "gold", "purple", "pink", "lightblue", "lightgreen"]

class Particle:
    def __init__(self, x, y):
        self.x = x
        self.y = y
        self.vx = random.uniform(-3, 3)
        self.vy = random.uniform(-3, 3)
        self.color = random.choice(colors)
        self.size = random.uniform(0.3, 1.5)
        self.life = random.uniform(50, 150)
        self.max_life = self.life

    def update(self):
        self.x += self.vx
        self.y += self.vy
        self.life -= 1

        # Bounce off edges
        if abs(self.x) > 580:
            self.vx *= -0.8
        if abs(self.y) > 380:
            self.vy *= -0.8

        # Add some gravity and randomness
        self.vy -= 0.1
        self.vx += random.uniform(-0.1, 0.1)
        self.vy += random.uniform(-0.1, 0.1)

# Particle system
particles = []

def create_explosion(x, y, count=20):
    for _ in range(count):
        particles.append(Particle(x, y))

def draw_3d_cube(turtle_obj, x, y, size, angle):
    """Draw a pseudo-3D rotating cube"""
    turtle_obj.penup()
    turtle_obj.goto(x, y)
    turtle_obj.pendown()

    # Calculate 3D rotation
    cos_a = math.cos(angle)
    sin_a = math.sin(angle)

    # Define cube vertices in 3D
    vertices = [
        [-size, -size, -size], [size, -size, -size], [size, size, -size], [-size, size, -size],
        [-size, -size, size], [size, -size, size], [size, size, size], [-size, size, size]
    ]

    # Project 3D to 2D with rotation
    projected = []
    for vertex in vertices:
        # Rotate around Y axis
        rotated_x = vertex[0] * cos_a - vertex[2] * sin_a
        rotated_z = vertex[0] * sin_a + vertex[2] * cos_a

        # Simple perspective projection
        scale = 200 / (200 + rotated_z)
        proj_x = x + rotated_x * scale
        proj_y = y + vertex[1] * scale
        projected.append((proj_x, proj_y))

    # Draw cube edges
    edges = [(0,1), (1,2), (2,3), (3,0), (4,5), (5,6), (6,7), (7,4), (0,4), (1,5), (2,6), (3,7)]

    for edge in edges:
        start, end = projected[edge[0]], projected[edge[1]]
        turtle_obj.penup()
        turtle_obj.goto(start[0], start[1])
        turtle_obj.pendown()
        turtle_obj.goto(end[0], end[1])

def draw_spiral_galaxy(turtle_obj, x, y, angle, size):
    """Draw a rotating spiral galaxy effect"""
    turtle_obj.penup()
    turtle_obj.goto(x, y)
    turtle_obj.pendown()

    for i in range(100):
        radius = i * size / 20
        spiral_angle = angle + i * 0.3

        new_x = x + radius * math.cos(spiral_angle)
        new_y = y + radius * math.sin(spiral_angle)

        turtle_obj.goto(new_x, new_y)

        # Change color gradually
        if i % 10 == 0:
            turtle_obj.color(random.choice(colors))

def main_animation():
    frame = 0

    print("🚀 EXTREME 3D ANIMATION STARTING!")
    print("🎆 Watch the incredible visual effects!")
    print("❌ Close the window to stop")

    try:
        while True:
            screen.clear()
            screen.bgcolor("black")

            # Create random explosions
            if random.random() < 0.1:
                create_explosion(random.uniform(-400, 400), random.uniform(-200, 200))

            # Draw multiple rotating 3D cubes
            for i, t in enumerate(turtles[:4]):
                t.color(colors[i % len(colors)])
                angle = frame * 0.05 + i * math.pi / 2
                x = 200 * math.cos(angle + i)
                y = 150 * math.sin(angle * 1.3 + i)
                draw_3d_cube(t, x, y, 30 + 10 * math.sin(frame * 0.1), frame * 0.1 + i)

            # Draw spiral galaxies
            for i, t in enumerate(turtles[4:]):
                t.color(colors[(i + 4) % len(colors)])
                angle = frame * 0.03 + i * math.pi / 3
                x = 300 * math.cos(angle * 0.7)
                y = 200 * math.sin(angle * 1.1)
                draw_spiral_galaxy(t, x, y, frame * 0.05 + i, 2 + math.sin(frame * 0.1))

            # Update and draw particles
            for particle in particles[:]:
                particle.update()
                if particle.life <= 0:
                    particles.remove(particle)
                else:
                    # Draw particle
                    alpha = particle.life / particle.max_life
                    turtle.penup()
                    turtle.goto(particle.x, particle.y)
                    turtle.pendown()
                    turtle.color(particle.color)
                    turtle.dot(particle.size * alpha * 10)

            # Draw connecting lines between nearby particles (web effect)
            if len(particles) > 1 and frame % 3 == 0:
                for i, p1 in enumerate(particles[:20]):  # Limit for performance
                    for p2 in particles[i+1:i+5]:
                        dist = math.sqrt((p1.x - p2.x)**2 + (p1.y - p2.y)**2)
                        if dist < 100:
                            turtle.penup()
                            turtle.goto(p1.x, p1.y)
                            turtle.pendown()
                            turtle.color("cyan")
                            turtle.pensize(1)
                            turtle.goto(p2.x, p2.y)

            # Add title
            turtle.penup()
            turtle.goto(0, 350)
            turtle.color(colors[frame % len(colors)])
            turtle.write(f"🔥 EXTREME 3D ANIMATION 🔥 Frame: {frame}",
                        align="center", font=("Arial", 16, "bold"))

            screen.update()
            frame += 1
            time.sleep(0.05)  # Control animation speed

    except turtle.Terminator:
        print("Animation stopped!")
    except KeyboardInterrupt:
        print("Animation interrupted!")
    finally:
        screen.bye()

if __name__ == "__main__":
    main_animation()
