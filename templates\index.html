<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LAN Device Scanner</title>
    <style>
        :root {
            --background: 0 0% 100%;
            --foreground: 222.2 84% 4.9%;
            --card: 0 0% 100%;
            --card-foreground: 222.2 84% 4.9%;
            --popover: 0 0% 100%;
            --popover-foreground: 222.2 84% 4.9%;
            --primary: 222.2 47.4% 11.2%;
            --primary-foreground: 210 40% 98%;
            --secondary: 210 40% 96%;
            --secondary-foreground: 222.2 84% 4.9%;
            --muted: 210 40% 96%;
            --muted-foreground: 215.4 16.3% 46.9%;
            --accent: 210 40% 96%;
            --accent-foreground: 222.2 84% 4.9%;
            --destructive: 0 84.2% 60.2%;
            --destructive-foreground: 210 40% 98%;
            --border: 214.3 31.8% 91.4%;
            --input: 214.3 31.8% 91.4%;
            --ring: 222.2 84% 4.9%;
            --radius: 0.5rem;
        }

        .dark {
            --background: 222.2 84% 4.9%;
            --foreground: 210 40% 98%;
            --card: 222.2 84% 4.9%;
            --card-foreground: 210 40% 98%;
            --popover: 222.2 84% 4.9%;
            --popover-foreground: 210 40% 98%;
            --primary: 210 40% 98%;
            --primary-foreground: 222.2 47.4% 11.2%;
            --secondary: 217.2 32.6% 17.5%;
            --secondary-foreground: 210 40% 98%;
            --muted: 217.2 32.6% 17.5%;
            --muted-foreground: 215 20.2% 65.1%;
            --accent: 217.2 32.6% 17.5%;
            --accent-foreground: 210 40% 98%;
            --destructive: 0 62.8% 30.6%;
            --destructive-foreground: 210 40% 98%;
            --border: 217.2 32.6% 17.5%;
            --input: 217.2 32.6% 17.5%;
            --ring: 212.7 26.8% 83.9%;
        }

        * {
            border-color: hsl(var(--border));
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: hsl(var(--background));
            color: hsl(var(--foreground));
            line-height: 1.5;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            margin-bottom: 3rem;
        }

        .header h1 {
            font-size: 2.25rem;
            font-weight: 700;
            color: hsl(var(--foreground));
            margin-bottom: 0.5rem;
            letter-spacing: -0.025em;
        }

        .header p {
            color: hsl(var(--muted-foreground));
            font-size: 1rem;
        }

        .controls {
            display: flex;
            gap: 0.75rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            border-radius: calc(var(--radius) - 2px);
            font-size: 0.875rem;
            font-weight: 500;
            height: 2.5rem;
            padding: 0 1rem;
            border: 1px solid hsl(var(--border));
            background-color: hsl(var(--background));
            color: hsl(var(--foreground));
            cursor: pointer;
            transition: all 0.2s ease-in-out;
        }

        .btn:hover {
            background-color: hsl(var(--accent));
            color: hsl(var(--accent-foreground));
        }

        .btn-primary {
            background-color: hsl(var(--primary));
            color: hsl(var(--primary-foreground));
            border-color: hsl(var(--primary));
        }

        .btn-primary:hover {
            background-color: hsl(var(--primary) / 0.9);
        }

        .btn:disabled {
            pointer-events: none;
            opacity: 0.5;
        }

        .card {
            background-color: hsl(var(--card));
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
            box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
        }

        .card-header {
            padding: 1.5rem;
            border-bottom: 1px solid hsl(var(--border));
        }

        .card-content {
            padding: 1.5rem;
        }

        .status-card {
            margin-bottom: 2rem;
        }

        .status-card .card-content {
            padding: 1rem 1.5rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 2rem;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: hsl(var(--foreground));
            margin-bottom: 0.25rem;
        }

        .stat-label {
            color: hsl(var(--muted-foreground));
            font-size: 0.875rem;
        }

        .devices-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 1.5rem;
        }

        .device-card {
            transition: box-shadow 0.2s ease-in-out;
        }

        .device-card:hover {
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        }

        .device-header {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .device-icon {
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 3rem;
            height: 3rem;
            background-color: hsl(var(--muted));
            border-radius: var(--radius);
        }

        .device-title h3 {
            font-size: 1.125rem;
            font-weight: 600;
            color: hsl(var(--card-foreground));
            margin-bottom: 0.25rem;
        }

        .device-ip {
            font-family: ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, monospace;
            color: hsl(var(--muted-foreground));
            font-size: 0.875rem;
        }

        .device-info {
            margin-top: 1rem;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid hsl(var(--border));
        }

        .info-row:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 500;
            color: hsl(var(--muted-foreground));
            font-size: 0.875rem;
        }

        .info-value {
            font-family: ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, monospace;
            font-size: 0.875rem;
            color: hsl(var(--foreground));
        }

        .status-online {
            color: #16a34a;
            font-weight: 500;
        }

        .status-offline {
            color: #dc2626;
            font-weight: 500;
        }

        .badge {
            display: inline-flex;
            align-items: center;
            border-radius: calc(var(--radius) - 2px);
            padding: 0.125rem 0.625rem;
            font-size: 0.75rem;
            font-weight: 500;
            background-color: hsl(var(--secondary));
            color: hsl(var(--secondary-foreground));
            border: 1px solid hsl(var(--border));
        }

        .ports-container {
            margin-top: 0.75rem;
        }

        .ports-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 0.375rem;
            margin-top: 0.5rem;
        }

        .loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 3rem;
            text-align: center;
        }

        .spinner {
            width: 2rem;
            height: 2rem;
            border: 2px solid hsl(var(--border));
            border-top: 2px solid hsl(var(--foreground));
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .no-devices {
            text-align: center;
            padding: 3rem;
            color: hsl(var(--muted-foreground));
        }

        .theme-toggle {
            position: fixed;
            top: 2rem;
            right: 2rem;
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .devices-grid {
                grid-template-columns: 1fr;
            }

            .controls {
                flex-direction: column;
            }

            .header h1 {
                font-size: 1.875rem;
            }

            .theme-toggle {
                top: 1rem;
                right: 1rem;
            }
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <button class="btn theme-toggle" onclick="toggleTheme()" id="theme-toggle">
        🌙
    </button>

    <div class="container">
        <div class="header">
            <h1>LAN Device Scanner</h1>
            <p>Discover and monitor devices on your local network</p>
        </div>

        <div class="controls">
            <button class="btn btn-primary" onclick="startScan()" id="scanBtn">
                <span>🔍</span>
                Scan Network
            </button>
            <button class="btn" onclick="refreshDevices()">
                <span>🔄</span>
                Refresh
            </button>
            <button class="btn" onclick="toggleNetworkInfo()">
                <span>📊</span>
                Network Info
            </button>
        </div>

        <div id="network-stats" class="card status-card hidden">
            <div class="card-content">
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value" id="total-devices">0</div>
                        <div class="stat-label">Devices Found</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="network-range">-</div>
                        <div class="stat-label">Network Range</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="scan-time">-</div>
                        <div class="stat-label">Last Scan</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card status-card">
            <div class="card-content">
                <p id="status">Ready to scan your network...</p>
            </div>
        </div>

        <div id="loading" class="card hidden">
            <div class="loading">
                <div class="spinner"></div>
                <p>Scanning network for devices...</p>
            </div>
        </div>

        <div id="devices-container">
            <div class="no-devices">
                Click "Scan Network" to discover devices on your LAN
            </div>
        </div>
    </div>

    <script>
        let scanInterval;
        let isDarkMode = false;

        function toggleTheme() {
            isDarkMode = !isDarkMode;
            const body = document.body;
            const toggle = document.getElementById('theme-toggle');
            
            if (isDarkMode) {
                body.classList.add('dark');
                toggle.textContent = '☀️';
            } else {
                body.classList.remove('dark');
                toggle.textContent = '🌙';
            }
        }

        function startScan() {
            const btn = document.getElementById('scanBtn');
            btn.disabled = true;
            btn.innerHTML = '<span>🔍</span> Scanning...';
            
            document.getElementById('loading').classList.remove('hidden');
            document.getElementById('devices-container').innerHTML = '';
            
            fetch('/api/scan')
                .then(response => response.json())
                .then(data => {
                    updateStatus('Scanning network... This may take a few minutes.');
                    scanInterval = setInterval(checkScanProgress, 2000);
                })
                .catch(error => {
                    console.error('Error starting scan:', error);
                    updateStatus('Error starting scan. Please try again.');
                    btn.disabled = false;
                    btn.innerHTML = '<span>🔍</span> Scan Network';
                    document.getElementById('loading').classList.add('hidden');
                });
        }

        function checkScanProgress() {
            fetch('/api/devices')
                .then(response => response.json())
                .then(data => {
                    if (!data.scanning) {
                        clearInterval(scanInterval);
                        document.getElementById('scanBtn').disabled = false;
                        document.getElementById('scanBtn').innerHTML = '<span>🔍</span> Scan Network';
                        document.getElementById('loading').classList.add('hidden');
                        
                        displayDevices(data);
                    } else {
                        displayDevices(data);
                    }
                })
                .catch(error => {
                    console.error('Error checking scan progress:', error);
                    clearInterval(scanInterval);
                    document.getElementById('scanBtn').disabled = false;
                    document.getElementById('scanBtn').innerHTML = '<span>🔍</span> Scan Network';
                    document.getElementById('loading').classList.add('hidden');
                });
        }

        function refreshDevices() {
            fetch('/api/devices')
                .then(response => response.json())
                .then(data => displayDevices(data))
                .catch(error => console.error('Error refreshing devices:', error));
        }

        function toggleNetworkInfo() {
            const statsDiv = document.getElementById('network-stats');
            if (statsDiv.classList.contains('hidden')) {
                statsDiv.classList.remove('hidden');
                fetch('/api/network-info')
                    .then(response => response.json())
                    .then(data => {
                        document.getElementById('network-range').textContent = data.network_range;
                    });
            } else {
                statsDiv.classList.add('hidden');
            }
        }

        function displayDevices(data) {
            const container = document.getElementById('devices-container');
            const devices = data.devices;

            document.getElementById('total-devices').textContent = data.total_devices;
            document.getElementById('scan-time').textContent = formatDate(data.last_scan);

            updateStatus(`Found ${data.total_devices} online devices. Last scan: ${formatDate(data.last_scan)}`);

            if (Object.keys(devices).length === 0) {
                container.innerHTML = '<div class="no-devices">No devices found. Try scanning again.</div>';
                return;
            }

            let html = '<div class="devices-grid">';
            
            Object.values(devices).forEach(device => {
                html += `
                    <div class="card device-card">
                        <div class="card-header">
                            <div class="device-header">
                                <div class="device-icon">${getDeviceIcon(device)}</div>
                                <div class="device-title">
                                    <h3>${device.hostname || 'Unknown Device'}</h3>
                                    <div class="device-ip">${device.ip}</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card-content">
                            <div class="device-info">
                                <div class="info-row">
                                    <span class="info-label">Status</span>
                                    <span class="info-value status-${device.status}">${device.status.toUpperCase()}</span>
                                </div>
                                
                                ${device.mac ? `
                                <div class="info-row">
                                    <span class="info-label">MAC Address</span>
                                    <span class="info-value">${device.mac}</span>
                                </div>
                                ` : ''}
                                
                                ${device.vendor && device.vendor !== 'Unknown' ? `
                                <div class="info-row">
                                    <span class="info-label">Vendor</span>
                                    <span class="info-value">${device.vendor}</span>
                                </div>
                                ` : ''}
                                
                                ${device.os ? `
                                <div class="info-row">
                                    <span class="info-label">OS</span>
                                    <span class="info-value">${device.os}</span>
                                </div>
                                ` : ''}
                            </div>
                            
                            ${device.ports && device.ports.length > 0 ? `
                            <div class="ports-container">
                                <div class="info-label">Open Ports</div>
                                <div class="ports-grid">
                                    ${device.ports.map(port => 
                                        `<span class="badge">${port.port}/${port.service}</span>`
                                    ).join('')}
                                </div>
                            </div>
                            ` : ''}
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            container.innerHTML = html;
        }

        function getDeviceIcon(device) {
            if (device.icon) return device.icon;
            
            // Default icon based on device type or vendor
            const hostname = (device.hostname || '').toLowerCase();
            const vendor = (device.vendor || '').toLowerCase();
            
            if (hostname.includes('router') || hostname.includes('gateway')) return '🌐';
            if (hostname.includes('printer')) return '🖨️';
            if (hostname.includes('phone') || hostname.includes('mobile')) return '📱';
            if (hostname.includes('laptop') || hostname.includes('macbook')) return '💻';
            if (vendor.includes('apple')) return '🍎';
            if (vendor.includes('samsung')) return '📱';
            if (vendor.includes('hp') || vendor.includes('canon') || vendor.includes('epson')) return '🖨️';
            
            return '💻';
        }

        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }

        function formatDate(dateString) {
            if (!dateString) return 'Never';
            const date = new Date(dateString);
            return date.toLocaleString();
        }

        // Auto-refresh every 30 seconds
        setInterval(refreshDevices, 30000);
        
        // Initial load
        refreshDevices();
    </script>
</body>
</html>