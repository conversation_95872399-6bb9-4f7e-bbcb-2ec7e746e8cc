#!/usr/bin/env python3
"""
Simple LAN Device Scanner with Web Interface
Scans local network for devices without requiring nmap
"""

import socket
import subprocess
import threading
import time
import json
from datetime import datetime
import ipaddress
import re
import platform
from flask import Flask, render_template, jsonify

app = Flask(__name__)

class SimpleLANScanner:
    def __init__(self):
        self.devices = {}
        self.scanning = False
        self.last_scan = None
        
    def get_local_ip(self):
        """Get the local IP address"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except:
            return "***********"
    
    def get_network_range(self):
        """Get the network range to scan"""
        local_ip = self.get_local_ip()
        network = ipaddress.IPv4Network(f"{local_ip}/24", strict=False)
        return str(network)
    
    def get_device_icon(self, device_info):
        """Determine device icon based on device information"""
        hostname = device_info.get('hostname', '').lower()
        
        # Router/Gateway detection
        if any(keyword in hostname for keyword in ['router', 'gateway', 'modem', 'fritz', 'speedport']):
            return '🌐'
            
        # Mobile devices
        if any(keyword in hostname for keyword in ['iphone', 'android', 'mobile', 'phone', 'samsung', 'huawei']):
            return '📱'
            
        # Computers
        if any(keyword in hostname for keyword in ['pc', 'desktop', 'laptop', 'macbook', 'imac', 'windows', 'ubuntu']):
            return '💻'
            
        # Smart TV / Media devices
        if any(keyword in hostname for keyword in ['tv', 'roku', 'chromecast', 'firestick', 'appletv', 'smart']):
            return '📺'
            
        # IoT / Smart devices
        if any(keyword in hostname for keyword in ['alexa', 'echo', 'nest', 'iot', 'sensor']):
            return '🏠'
            
        # Printers
        if any(keyword in hostname for keyword in ['printer', 'print', 'canon', 'epson', 'hp']):
            return '🖨️'
            
        # Gaming consoles
        if any(keyword in hostname for keyword in ['xbox', 'playstation', 'nintendo', 'switch']):
            return '🎮'
            
        # Raspberry Pi
        if any(keyword in hostname for keyword in ['raspberry', 'pi', 'raspberrypi']):
            return '🥧'
            
        return '🔧'
    
    def ping_host(self, ip):
        """Ping a host to check if it's alive"""
        try:
            if platform.system().lower() == "windows":
                result = subprocess.run(['ping', '-n', '1', '-w', '1000', ip], 
                                      capture_output=True, text=True, timeout=3)
                return result.returncode == 0
            else:
                result = subprocess.run(['ping', '-c', '1', '-W', '1', ip], 
                                      capture_output=True, text=True, timeout=3)
                return result.returncode == 0
        except:
            return False
    
    def get_hostname(self, ip):
        """Get hostname for an IP address"""
        try:
            hostname = socket.gethostbyaddr(ip)[0]
            return hostname
        except:
            return f"Device-{ip.split('.')[-1]}"
    
    def get_mac_address(self, ip):
        """Get MAC address using ARP table"""
        try:
            if platform.system().lower() == "windows":
                result = subprocess.run(['arp', '-a', ip], capture_output=True, text=True, timeout=3)
                if result.returncode == 0:
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if ip in line:
                            # Extract MAC address from ARP output
                            mac_match = re.search(r'([0-9a-fA-F]{2}[:-]){5}[0-9a-fA-F]{2}', line)
                            if mac_match:
                                return mac_match.group(0).replace('-', ':').lower()
            else:
                result = subprocess.run(['arp', '-n', ip], capture_output=True, text=True, timeout=3)
                if result.returncode == 0:
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if ip in line:
                            parts = line.split()
                            if len(parts) >= 3:
                                return parts[2].lower()
        except:
            pass
        return ""
    
    def check_common_ports(self, ip):
        """Check common ports on a device"""
        common_ports = [22, 23, 53, 80, 135, 139, 443, 445, 993, 995, 8080]
        open_ports = []
        
        for port in common_ports:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(0.5)
                result = sock.connect_ex((ip, port))
                if result == 0:
                    service = self.get_service_name(port)
                    open_ports.append({'port': port, 'service': service})
                sock.close()
            except:
                pass
        
        return open_ports
    
    def get_service_name(self, port):
        """Get service name for a port"""
        services = {
            22: 'SSH',
            23: 'Telnet',
            53: 'DNS',
            80: 'HTTP',
            135: 'RPC',
            139: 'NetBIOS',
            443: 'HTTPS',
            445: 'SMB',
            993: 'IMAPS',
            995: 'POP3S',
            8080: 'HTTP-Alt'
        }
        return services.get(port, 'Unknown')
    
    def scan_device(self, ip):
        """Scan a single device"""
        if not self.ping_host(ip):
            return None
            
        device_info = {
            'ip': ip,
            'hostname': self.get_hostname(ip),
            'mac': self.get_mac_address(ip),
            'vendor': 'Unknown',
            'ports': self.check_common_ports(ip),
            'status': 'online',
            'last_seen': datetime.now().isoformat()
        }
        
        device_info['icon'] = self.get_device_icon(device_info)
        return device_info
    
    def scan_network(self):
        """Scan the entire network"""
        if self.scanning:
            return
            
        self.scanning = True
        print("🔍 Starting network scan...")
        
        network_range = self.get_network_range()
        print(f"📡 Scanning network: {network_range}")
        
        self.devices = {}
        network = ipaddress.IPv4Network(network_range)
        
        # Use threading for faster scanning
        threads = []
        for ip in network.hosts():
            ip_str = str(ip)
            thread = threading.Thread(target=self._scan_and_store, args=(ip_str,))
            threads.append(thread)
            thread.start()
            
            # Limit concurrent threads
            if len(threads) >= 50:
                for t in threads:
                    t.join()
                threads = []
        
        # Wait for remaining threads
        for thread in threads:
            thread.join()
        
        self.last_scan = datetime.now()
        self.scanning = False
        online_count = len([d for d in self.devices.values() if d['status'] == 'online'])
        print(f"✅ Scan complete! Found {online_count} online devices")
    
    def _scan_and_store(self, ip):
        """Helper method to scan and store device info"""
        device_info = self.scan_device(ip)
        if device_info:
            self.devices[ip] = device_info

# Global scanner instance
scanner = SimpleLANScanner()

@app.route('/')
def index():
    """Main page"""
    return render_template('index.html')

@app.route('/api/devices')
def get_devices():
    """API endpoint to get device list"""
    online_devices = {ip: info for ip, info in scanner.devices.items() if info['status'] == 'online'}
    return jsonify({
        'devices': online_devices,
        'total_devices': len(online_devices),
        'last_scan': scanner.last_scan.isoformat() if scanner.last_scan else None,
        'scanning': scanner.scanning
    })

@app.route('/api/scan')
def start_scan():
    """API endpoint to start a new scan"""
    if not scanner.scanning:
        thread = threading.Thread(target=scanner.scan_network)
        thread.daemon = True
        thread.start()
        return jsonify({'status': 'scan_started'})
    else:
        return jsonify({'status': 'already_scanning'})

@app.route('/api/network-info')
def get_network_info():
    """Get network information"""
    local_ip = scanner.get_local_ip()
    network_range = scanner.get_network_range()
    return jsonify({
        'local_ip': local_ip,
        'network_range': network_range
    })

if __name__ == '__main__':
    print("🚀 Starting Simple LAN Scanner Web Server...")
    print("📡 This will scan your local network for devices")
    print("🌐 Web interface will be available at: http://localhost:5000")
    print("💡 This version works without nmap - using basic network tools")
    
    # Show network info
    local_ip = scanner.get_local_ip()
    network_range = scanner.get_network_range()
    print(f"🏠 Your IP: {local_ip}")
    print(f"📊 Scanning range: {network_range}")
    
    # Start initial scan in background
    initial_scan_thread = threading.Thread(target=scanner.scan_network)
    initial_scan_thread.daemon = True
    initial_scan_thread.start()
    
    # Start web server
    app.run(host='0.0.0.0', port=5000, debug=False)
